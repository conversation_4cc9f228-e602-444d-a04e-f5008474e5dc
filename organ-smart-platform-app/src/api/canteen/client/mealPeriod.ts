import service from "@/service";
import { IMealPeriod, IUrgeRequest } from "./types/mealPeriod";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/mealPeriod`;

/** 是否模拟数据 */
const isMock = true;

/**
 * 获取食堂的餐次餐次信息
 * @param params: { canteenId: string; }
 * @returns 餐次信息列表
 */
export function getMealPeriodsByCanteenAndDate(params: { canteenId: string }) {
  if (isMock) {
    return new Promise<IMealPeriod[]>((resolve) => {
      const mealPeriods: IMealPeriod[] = [
        {
          id: "1",
          canteenId: params.canteenId,
          mealName: "午餐",
          startTime: "11:00",
          endTime: "13:00",
          advanceBookingOpenTime: "20:00",
          advanceBookingCloseTime: "23:00",
          advanceCancelCloseTime: "09:00",
          tempBookingOpenTime: "08:00",
          tempBookingCloseTime: "10:00",
          tempCancelCloseTime: "10:30",
          advanceBookingSeats: -1,
          availableSeats: 29,
          tempBookingSeats: 50,
          quotaCount: 50,
          quotaLeft: 0,
          status: "AVAILABLE",
          bookingAvailableStatus: "OPEN",
          isUrged: false,
          urgeCount: 0,
        },
        {
          id: "2",
          canteenId: params.canteenId,
          mealName: "晚餐",
          startTime: "17:00",
          endTime: "19:00",
          advanceBookingOpenTime: "20:00",
          advanceBookingCloseTime: "23:00",
          advanceCancelCloseTime: "09:00",
          tempBookingOpenTime: "08:00",
          tempBookingCloseTime: "20:00",
          tempCancelCloseTime: "16:00",
          advanceBookingSeats: -1,
          availableSeats: 49,
          tempBookingSeats: 50,
          quotaCount: 50,
          quotaLeft: 49,
          status: "AVAILABLE",
          bookingAvailableStatus: "FULL",
          isUrged: false,
          urgeCount: 0,
        },
      ];
      resolve(mealPeriods);
    });
  }
  return service.get<typeof params, IMealPeriod[]>(
    `${MODULE_API_PREFIX}/list`,
    { params }
  );
}

/**
 * 提交催发请求
 * @param urgeRequest 催发请求数据
 * @returns 催发结果
 */
export function submitUrgeRequest(urgeRequest: IUrgeRequest) {
  if (isMock) {
    return new Promise<{ success: boolean; message: string }>((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: "催发请求已成功提交",
        });
      }, 3000);
    });
  }
  return service.post<any, { success: boolean; message: string }>(
    `${MODULE_API_PREFIX}/urge`,
    urgeRequest
  );
}

/**
 * 取消预约
 * @param bookingId 预约ID
 * @returns 取消结果
 */
export function cancelBooking(bookingId: string) {
  if (isMock) {
    return new Promise<boolean>((resolve) => {
      resolve(true);
    });
  }
  return service.post<any, boolean>(`${MODULE_API_PREFIX}/cancel`, {
    bookingId,
  });
}

/**
 * 获取用户预约记录
 * @param params 查询参数
 * @returns 预约记录列表
 */
export function getUserBookings(params: {
  pageNo?: number;
  pageSize?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
}) {
  if (isMock) {
    return new Promise<any>((resolve) => {
      resolve({
        total: 10,
        list: [
          {
            id: "1",
            canteenName: "学苑食堂",
            mealName: "午餐",
            bookingDate: "2024-08-28",
            bookingTime: "11:00-13:00",
            status: "CONFIRMED",
            createTime: "2024-08-27 10:30:00",
          },
          {
            id: "2",
            canteenName: "学苑食堂",
            mealName: "晚餐",
            bookingDate: "2024-08-28",
            bookingTime: "17:00-19:00",
            status: "PENDING",
            createTime: "2024-08-27 10:35:00",
          },
        ],
      });
    });
  }
  return service.get<typeof params, any>(`${MODULE_API_PREFIX}/user-bookings`, {
    params,
  });
}
