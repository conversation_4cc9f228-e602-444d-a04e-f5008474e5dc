<template>
  <div class="booking-card">
    <div class="card-header">
      <div class="canteen-name">{{ bookingInfo.canteenName }}</div>
      <div class="canteen-tag booking-status" :style="getStatusStyle">
        {{ bookingInfo.statusLabel }}
      </div>
    </div>
    <div class="dining-info">
      <div class="canteen-tag plain">{{ bookingInfo.mealName }}</div>
      <div>{{ bookingInfo.mealTime }}</div>
      <div>{{ formattedDiningDate }}</div>
    </div>
    <div
      v-if="bookingInfo.status === BookingStatusEnum.CONFIRMED"
      class="actions"
    >
      <div class="canteen-btn small action-btn" @click="handleViewDiningCode">
        查看就餐码
      </div>
      <div
        class="canteen-btn small btn-primary danger action-btn"
        :class="{ loading: cancelLoading }"
        @click="handleCancelBooking"
      >
        {{ cancelLoading ? "取消中..." : "取消预约" }}
      </div>
    </div>

    <!-- 就餐码弹窗 -->
    <DingCodeDialog v-model="showQRCode" :bookingInfo="bookingInfo" />

    <!-- 取消预约确认弹框 -->
    <CancelBookingDialog
      v-model="showCancelDialog"
      :bookingInfo="bookingInfo"
      @confirm="handleConfirmCancel"
      @cancel="handleCancelDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
import DingCodeDialog from "./dingCodeDialog.vue";
import { IUserBooking } from "@/api/canteen/client/types/booking";
import { BookingStatusEnum } from "@/views/canteen/configs/enums/BookingEnums";
import CancelBookingDialog from "./cancelBookingDialog.vue";
import { cancelBooking } from "@/api/canteen/client/mealPeriod";
import { useToast } from "@/components/Toast";
import dayjs from "dayjs";
import { withDefaults } from "vue";

/** 外部传入的预约信息 */
interface IProps {
  bookingInfo: IUserBooking;
}

/** 组件事件接口 */
interface IEmits {
  /** 预约更新后事件（用于刷新父组件数据） */
  (e: "refresh"): void;
}

/** 预约状态的样式配置 */
interface IStatusStyle {
  backgroundColor: string;
  color: string;
}

/** 默认的预约状态样式配置 */
const statusStyleConfig: Record<BookingStatusEnum | "DEFAULT", IStatusStyle> = {
  [BookingStatusEnum.CONFIRMED]: {
    backgroundColor: "#FFF7EB",
    color: "#F39E16",
  },
  [BookingStatusEnum.CANCELLED]: {
    backgroundColor: "#FEF0F0",
    color: "#FF4D4F",
  },
  [BookingStatusEnum.VERIFIED]: {
    backgroundColor: "#F0F9EB",
    color: "#1CC960",
  },
  [BookingStatusEnum.EXPIRED]: {
    backgroundColor: "#F4F4F5",
    color: "#A6A6A6",
  },
  DEFAULT: {
    backgroundColor: "var(--color-primary-plain-bg)",
    color: "var(--color-primary)",
  },
};

const props = withDefaults(defineProps<IProps>(), {
  bookingInfo: () => ({} as IUserBooking),
});

const emits = defineEmits<IEmits>();

/** Toast 提示 */
const toast = useToast();

/** 获取预约状态的样式配置 */
const getStatusStyle = computed(() => {
  const status = props.bookingInfo.status;
  if (status in statusStyleConfig) {
    return statusStyleConfig[status];
  }
  return statusStyleConfig.DEFAULT;
});

/** 格式化就餐日期 */
const formattedDiningDate = computed(() => {
  if (!props.bookingInfo.bookingDate) return "";
  return dayjs(props.bookingInfo.bookingDate).format("M月D日");
});

/** 是否显示就餐码 */
const showQRCode = ref(false);

/** 是否显示取消预约确认弹框 */
const showCancelDialog = ref(false);

/** 取消预约loading状态 */
const cancelLoading = ref(false);

/** 点击查看就餐码 */
const handleViewDiningCode = () => {
  showQRCode.value = true;
};

/** 点击取消预约 */
const handleCancelBooking = () => {
  // 如果正在取消中，则不允许再次点击
  if (cancelLoading.value) {
    return;
  }
  showCancelDialog.value = true;
};

/**
 * 处理确认取消预约
 */
const handleConfirmCancel = async () => {
  if (cancelLoading.value) {
    return; // 防止重复提交
  }

  try {
    cancelLoading.value = true;

    // 调用取消预约接口
    const result = await cancelBooking(props.bookingInfo.id);

    if (result) {
      toast.success("取消预约成功");

      // 通知父组件刷新数据
      emits("refresh");
      debugger;
      // 关闭确认弹框
      showCancelDialog.value = false;
    }
  } catch (error) {
    console.error("取消预约失败:", error);
    toast.error("取消预约失败，请稍后重试");
  } finally {
    cancelLoading.value = false;
  }
};

/**
 * 处理取消确认弹框关闭
 */
const handleCancelDialogClose = () => {
  showCancelDialog.value = false;
};
</script>

<style scoped lang="scss">
.booking-card {
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: #fff;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .canteen-name {
      font-size: 18px;
      color: #000000;
      margin: 0;
    }
  }

  .dining-info {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    column-gap: 16px;

    color: #383838;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    column-gap: 16px;

    .action-btn {
      font-size: 16px;
      transition: all 0.3s ease;

      &.loading {
        opacity: 0.6;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}
</style>
