<template>
  <div class="my-booking-page">
    <!-- <div
    class="canteen-page-with-header canteen-page-with-tabbar my-booking-page"
  > -->
    <MobileHeader title="我的预约" />
    <div class="header-gap"></div>

    <div class="canteen-list-page canteen-content-px list-content">
      <AutoLoad
        :loading="loading"
        :refreshing="refreshing"
        :has-more="hasMore"
        :show-empty="isEmpty"
        @load-more="loadMore"
        @refresh="refresh"
      >
        <template v-for="item in data">
          <BookingCard
            class="mt-20"
            :key="item.id"
            :bookingInfo="item"
            @refresh="handleAfterUpdate"
          />
        </template>

        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">📋</div>
            <h3>暂无数据</h3>
            <p>点击刷新按钮加载数据</p>
            <button @click="refresh" class="refresh-btn">刷新数据</button>
          </div>
        </template>
      </AutoLoad>
    </div>
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import MobileHeader from "@/components/MobileHeader/index.vue";
import TabBar from "@/components/TabBar/index.vue";
import BookingCard from "./_comp/myBookings/bookingCard.vue";
import AutoLoad from "@/components/AutoLoad";
import { useAutoLoad } from "@/components/AutoLoad";
import { LoadDataParams } from "@/components/AutoLoad/types";
import { getUserBookingPage } from "@/api/canteen/client/booking";

const { data, loading, refreshing, hasMore, isEmpty, loadMore, refresh } =
  useAutoLoad({
    pageSize: 10,
    loadData: async (params: LoadDataParams) => {
      // 模拟 API 请求
      return getUserBookingPage(params).then((res) => {
        return {
          data: res.list,
          total: res.total,
        };
      });
    },
    onError: (error) => {
      console.error("Hook 加载失败:", error);
    },
  });

/**
 * 处理预约更新后事件
 * 当BookingCard组件触发refresh事件时调用
 */
const handleAfterUpdate = async () => {
  console.log("收到BookingCard的refresh事件，开始刷新数据");

  try {
    debugger;
    // 刷新预约列表数据
    refresh();
  } catch (error) {
    console.error("刷新预约列表数据失败:", error);
  }
};
</script>

<style scoped lang="scss">
.my-booking-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow-y: hidden !important;

  /* .list-content {
    padding: 0 20px;
  } */

  .mt-20 {
    margin-top: 10px;
  }
}
</style>
